<template>
  <VRadio
    v-if="useRadio"
    v-bind="props"
    v-model="model"
  />

  <VSelect
    v-else
    v-bind="props"
    v-model="model"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { type SelectProps, type RadioGroupProps } from 'element-plus'
  import { FormItem } from '../types'
  import VRadio from './v-radio.vue'
  import VSelect from './v-select.vue'

  const props = defineProps<Partial<SelectProps & RadioGroupProps & FormItem>>()

  const options = computed(() => {
    if (!props.ext_properties?.mapping?.mapping_values) return []
    return props.ext_properties.mapping.mapping_values
  })

  const useRadio = computed(() => {
    return options.value.length === 2
  })

  const model = defineModel<string>()

  defineOptions({
    name: 'VMapping'
  })
</script>
