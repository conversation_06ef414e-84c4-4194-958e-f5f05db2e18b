<template>
  <el-popover
    v-model:visible="popoverVisible"
    placement="bottom-end"
    :width="dynamicWidth"
    trigger="click"
    popper-class="q-select-popover"
    :disabled="props.disabled"
  >
    <template #reference>
      <div
        ref="triggerRef"
        class="text-28 flex items-center justify-between"
        :class="{
          'text-primary-description': !displayText,
          'cursor-not-allowed opacity-50': props.disabled
        }"
      >
        <span class="flex-1 truncate">{{ displayText || props.placeholder || '请选择' }}</span>
        <div class="flex items-center gap-2 ml-2">
          <!-- 清除按钮 -->
          <img
            v-if="props.clearable && displayText && !props.disabled"
            :src="clearIcon"
            width="24px"
            height="24px"
            alt="清除"
            @click.stop="handleClear"
          />

          <!-- 箭头图标 -->
          <img
            src="@/assets/arrow-down.svg"
            alt=""
            class="transition-transform duration-200 flex-shrink-0"
            :class="{ 'rotate-180': popoverVisible }"
          />
        </div>
      </div>
    </template>

    <main class="flex flex-col gap-2">
      <!-- 搜索框 -->
      <div v-if="filterable || options.length >= 5" class="shrink-0">
        <input
          ref="searchInputRef"
          v-model="searchKeyword"
          class="w-full py-3 px-4 border-b border-[#747EB254] focus:border-b-primary focus-visible:outline-none placeholder:text-[#747EB280] min-w-0"
          placeholder="请输入关键词搜索"
        />
      </div>
      <!-- 选项列表 -->
      <div class="flex-1 py-5">
        <el-scrollbar max-height="400px">
          <!-- 普通选项 -->
          <div
            v-for="option in filteredOptions"
            :key="option.key"
            class="q-select-option py-3 px-4 text-28 border-b border-b-gray-100 truncate"
            :class="{
              'text-info': option.key === model
            }"
            @click="handleOptionClick(option)"
          >
            {{ option.value }}
          </div>

          <!-- 无数据提示 -->
          <div
            v-if="filteredOptions.length === 0"
            class="px-3 py-2 text-28 text-primary-description text-center"
          >
            {{ searchKeyword ? '无匹配数据' : '暂无数据' }}
          </div>
        </el-scrollbar>
      </div>
    </main>
  </el-popover>
</template>

<script setup lang="ts">
  import { ElInput, ElPopover, ElScrollbar, type SelectProps } from 'element-plus'
  import { computed, inject, nextTick, ref, watch } from 'vue'
  import { FormItem } from '../types'
  import clearIcon from './clear.svg'

  const props = defineProps<Partial<SelectProps & FormItem>>()

  const options = computed(() => {
    if (!props.ext_properties?.mapping?.mapping_values) return []
    return props.ext_properties.mapping.mapping_values
  })

  const model = defineModel<string>()

  const handleParameterChange =
    inject<(property: string, value: any, param: FormItem) => void>('handleParameterChange')

  // 响应式数据
  const popoverVisible = ref(false)
  const searchKeyword = ref('')
  const triggerRef = ref<HTMLElement | null>(null)
  const searchInputRef = ref<InstanceType<typeof ElInput>>()

  // 计算属性
  const displayText = computed(() => {
    if (!model.value) return ''

    const selectedOption = options.value.find(option => option.key === model.value)
    return selectedOption ? selectedOption.value : ''
  })

  const dynamicWidth = computed(() => {
    if (!options.value || options.value.length === 0) {
      // 最小宽度
      return 300
    }

    // 计算最长选项的字符长度
    const maxLength = Math.max(...options.value.map(option => option.value.length), 10)

    // 每个字符大约 28px（对应 text-28），加上内边距
    const calculatedWidth = maxLength * 28 + 80 // 80px 为左右内边距和其他空间

    return Math.min(Math.max(calculatedWidth, 300), 500)
  })

  const filteredOptions = computed(() => {
    if (!options.value || options.value.length === 0) {
      return []
    }

    if (!searchKeyword.value) return options.value

    return options.value.filter(option => {
      const label = option.value.toLowerCase()
      const keyword = searchKeyword.value.toLowerCase()
      return label.includes(keyword)
    })
  })

  // 事件处理
  const handleClear = () => {
    const value = ''
    model.value = ''
    triggerParameterChange(value)
  }

  const handleOptionClick = (option: any) => {
    model.value = option.key
    popoverVisible.value = false
    triggerParameterChange(option.key)
  }

  const triggerParameterChange = (value: any) => {
    if (props.ext_properties?.is_param && props.property && handleParameterChange) {
      handleParameterChange(props.property, value, props as FormItem)
    }
  }

  // 监听弹窗状态
  watch(popoverVisible, visible => {
    if (visible) {
      // 如果可搜索，聚焦搜索框
      if (props.filterable) {
        nextTick(() => {
          searchInputRef.value?.focus()
        })
      }
    } else {
      searchKeyword.value = ''
    }
  })

  defineOptions({
    name: 'VSelect'
  })
</script>

<style scoped>
  .q-select-option:last-child {
    border-bottom: none;
  }
</style>

<style>
  .q-select-popover {
    border-radius: 20px !important;
    padding: 30 !important;
  }
</style>
