<template>
  <div class="flex flex-col h-screen">
    <QNavbar show-home>
      <template #right>
        <!-- 无法采集 -->
        <UnableToCollectModal
          :id="query.id"
          :root-task-id="query.rootTaskId"
        />
        <QButton
          class="ml-auto w-[176px]"
          :disabled="!canSumit"
          type="gradient"
          :loading="buildFormDataLoading || groupLoading"
          @click="handleSubmit"
          >提交</QButton
        >
      </template>
    </QNavbar>
    <!-- 顶部个人信息 -->
    <div class="px-14 pb-11 bg-white flex gap-4 items-center">
      <div class="name text-[64px] font-medium text-primary">{{ personal.name }}</div>
      <div>
        <div
          v-loading="buildFormDataLoading"
          class="text-[#4C6584] text-2xl font-normal flex gap-8 items-center"
        >
          <span>{{ ENUM_GENDER_MAP[personal.gender] }}</span>
          <span>{{ personal.age }}岁</span>
          <span>{{ encryptPhone(personal.mobile) }}</span>
          <span
            class="text-info border border-info rounded-full font-normal text-2xl w-[100px] h-[42px] flex items-center justify-center cursor-pointer"
            >联系
          </span>
        </div>
        <div class="tags mt-4 flex gap-[9px]">
          <QTag
            v-for="item in tags"
            :key="item.name"
            size="small"
            type="gray"
            >{{ item.name }}</QTag
          >
        </div>
      </div>
    </div>

    <main class="flex-1 overflow-y-auto bg-[#F1F5FD] box-border">
      <div class="header ml-11 mt-10">
        <!-- 滑动 -->
        <ul class="flex items-center flex-nowrap gap-5 overflow-x-auto relative hide-scrollbar">
          <li
            v-for="(item, index) in infoStepsWithActive"
            :key="item.title"
            class="item rounded-2xl min-w-[240px] h-[160px] flex flex-col items-center justify-center gap-3.5 shrink-0 box-border p-5"
            :class="[item.active ? ' text-white active' : ' text-primary bg-white']"
            @click="onClickItem(index)"
          >
            <div class="title font-medium text-32 text-center">{{ item.title }}</div>
            <div>
              <div class="text-4xl">
                <span
                  :class="[item.active ? 'text-white' : `text-${item.done ? 'success' : 'danger'}`]"
                >
                  {{ item.complete }}
                </span>
                <span :class="[item.active ? ' text-white' : ' text-primary-description']"
                  >/{{ item.total }}</span
                >
                <!-- <span
                  v-if="item.done"
                  class="text-2xl ml-2 font-light"
                  :class="[item.done ? ' text-white' : ' text-info']"
                  >已保存</span
                > -->
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="mt-5 mx-11 rounded-2xl p-4 bg-[#FBF3DFBF] text-[#664A29] text-2xl text-center">
        每组信息未填完或错误，数字红色，反之为绿色。所有数字绿色点击右上角提交按钮，即可完成采集~
      </div>
      <div class="mx-11 mt-10 box-border bg-white rounded-[20px] px-10 pt-10 pb-16">
        <div class="content">
          <h1 class="flex justify-center text-primary text-42 font-medium relative">
            {{ currentGroup?.title }}
            <!-- 不要删 -->
            <!-- <span
              class="inline-flex items-center gap-2 text-[28px] text-primary absolute right-0 top-1/2 transform -translate-y-1/2"
            >
              <img width="34" height="34" :src="getImageUrl('zhinengyuyin')" alt="" />
              智能语音采集
            </span> -->
          </h1>

          <div
            v-loading="buildFormDataLoading || groupLoading"
            class="form rounded-[15px] p-12 mt-10 bg-[#E5EAFF80] flex flex-col gap-10"
          >
            <QForm
              v-if="currentStepparameters"
              :key="`form-${activeStep}`"
              ref="formRef"
              :parameters="currentStepparameters"
              :rules="rules"
              :on-before-update-params="onBeforeUpdateParams"
              @parameters-updated="onParametersUpdated"
            >
              <template #mobile-input="{ value, updateValue }">
                <EncryptInput
                  :value="value"
                  encrypt-type="phone"
                  placeholder="请输入手机号"
                  @update:value="updateValue"
                />
              </template>
              <template #education_school_name-input="{ value, updateValue }">
                <EducationSchoolPicker
                  :model-value="value"
                  placeholder="请选择学校"
                  @update:model-value="updateValue"
                />
              </template>
              <template #work_company_name-input="{ value, updateValue }">
                <CompanyPicker
                  :model-value="value"
                  placeholder="请选择用人单位"
                  @update:model-value="updateValue"
                />
              </template>
            </QForm>
          </div>

          <div class="item flex items-center justify-center mt-16 gap-10">
            <QButton
              class="w-[394px]"
              ghost
              type="primary"
              size="large"
              :disabled="activeStep === 0"
              :loading="buildFormDataLoading || groupLoading"
              @click="onClickItem(Math.max(0, activeStep - 1))"
              >上一组</QButton
            >
            <QButton
              class="w-[454px]"
              type="gradient"
              size="large"
              :disabled="activeStep === infoStepsWithActive.length - 1"
              :loading="buildFormDataLoading || groupLoading || nextLoading"
              @click="handleSaveAndNext"
            >
              {{
                activeStep === infoStepsWithActive.length - 1 ? '已是最后一组' : '保存，填写下一组'
              }}
            </QButton>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
  import { QButton, QForm, QNavbar, QTag } from '@/components/q-components'
  import { formatFormData } from '@/components/q-components/q-form'
  import CompanyPicker from '@/components/q-components/q-form/extra/company-picker.vue'
  import EducationSchoolPicker from '@/components/q-components/q-form/extra/education-school-picker.vue'
  import EncryptInput from '@/components/q-components/q-form/field/EncryptInput.vue'
  import { FormItem } from '@/components/q-components/q-form/types'
  import { useSchema } from '@/stores/schema'
  import { ENUM_GENDER_MAP } from '@/utils/enum'
  import { encryptPhone } from '@/utils/formart'
  import { message } from '@/utils/message'
  import { sdk } from '@/utils/sdk'
  import { storeToRefs } from 'pinia'
  import { computed, onMounted, ref } from 'vue'
  import { useRequest } from 'vue-request'
  import { useRouter } from 'vue-router'
  import UnableToCollectModal from './components/UnableToCollectModal.vue'

  // 路由和参数
  const router = useRouter()
  const routeQuery = router.currentRoute.value.query
  const query = {
    modelName: String(routeQuery.modelName || ''),
    id: String(routeQuery.id || ''),
    title: String(routeQuery.title || ''),
    editEnable: String(routeQuery.editEnable || ''),
    rootTaskId: String(routeQuery.collect_root_task_id || ''),
    taskId: String(routeQuery.collect_task_id || ''),
    name: String(routeQuery.name || '')
  }
  // 表单引用
  const formRef = ref<InstanceType<typeof QForm> | null>()

  // images 不要删
  // const getImageUrl = (name: string, type: 'svg' | 'png' = 'svg') => {
  //   return new URL(`./images/${name}.${type}`, import.meta.url).href
  // }

  // 关于个人
  const personal = ref<{
    age: number
    name: string
    gender: string
    mobile: string
  }>({
    age: 0,
    name: '',
    gender: '',
    mobile: ''
  })
  const tags = ref([{ name: '退役军人' }, { name: '重点人群标签' }, { name: '有几个展示几个' }])

  // 分组数据
  const schemeStore = useSchema()
  const { currentSchema, loading: groupLoading } = storeToRefs(schemeStore)

  // 创建表单rules，从schema中获取，只计算一次
  const rules = computed(() => {
    try {
      if (!currentSchema.value || !buildFormData.value) return {}

      const formData = formatFormData(buildFormData.value)
      const mapFormData: Record<string, FormItem> = {}

      formData.formItems.forEach(item => {
        mapFormData[item.property!] = item
      })

      const rulesObj: Record<string, any[]> = {}

      // 遍历所有分组
      currentSchema.value.forEach(group => {
        // 遍历每个分组的字段
        group.fields.forEach(field => {
          // 检查该字段是否在实际表单项中存在
          if (!mapFormData[field.field]) return
          const fieldRules: any[] = []

          // 必填验证
          if (field.required) {
            fieldRules.push({
              required: true,
              message: `${field.label}不能为空`,
              trigger: 'blur'
            })
          }

          // 正则验证
          if (field.rule_regex) {
            try {
              const regex = new RegExp(field.rule_regex)
              fieldRules.push({
                pattern: regex,
                message: `${field.label}格式不正确`,
                trigger: 'blur'
              })
            } catch (error) {
              console.warn(`字段 ${field.field} 的正则表达式无效:`, field.rule_regex, error)
            }
          }

          // 如果有验证规则，则添加到规则对象中
          if (fieldRules.length > 0) {
            rulesObj[field.field] = fieldRules
          }
        })
      })

      return rulesObj
    } catch (error) {
      console.error('计算 rules 时出错:', error)
      return {}
    }
  })

  // 在组件挂载时获取任务信息
  onMounted(async () => {
    try {
      await schemeStore.getCurrentTaskInfo(query.taskId)
    } catch (error) {
      console.error('获取任务信息失败:', error)
    }
  })

  // 需要提交的表单数据，每一步的数据需要覆盖已有的表单数据，点击每一步或者最后提交的时候打印
  const submitFormData = ref<{ property: string; value: any }[]>([])

  // 表单数据
  const action = sdk.uniplatSdk.model('collect_task_order_detail').action('base_insert')
  const { data: buildFormData, loading: buildFormDataLoading } = useRequest(
    () =>
      action.query([{ v: 0, id: query.id }], [], {
        intentContext: {
          id: query.id,
          modelName: query.modelName || 'collect_task_order_wait_detail',
          name: query.name || 'insert_by_grid_intent'
        }
      }),
    {
      onSuccess(res) {
        const _tempSubmitFormData: { property: string; value: any }[] = []
        const personalKeys = Object.keys(personal.value)
        res.parameters.inputs_parameters.forEach(item => {
          _tempSubmitFormData.push({
            property: item.property,
            value: item.default_value
          })
          if (item.property && personalKeys.includes(item.property)) {
            const key = item.property as keyof typeof personal.value
            if (key in personal.value) {
              ;(personal.value as any)[key] = item.default_value
            }
          }
        })

        submitFormData.value = _tempSubmitFormData
      }
    }
  )

  // 更新表单
  const updatedFormItems = ref<FormItem[]>([])
  const onParametersUpdated = (newParams: FormItem[]) => {
    updatedFormItems.value = newParams
  }

  const onBeforeUpdateParams = async (): Promise<void> => {
    await new Promise<void>(resolve => {
      // submitFormData.value.forEach(item => {
      //   formRef.value?.updateFormValue(item.property, item.value)
      // })
      formRef.value?.addExtraParameters(submitFormData.value)
      resolve()
    })
  }

  // 从0开始，对应数组索引
  const activeStep = ref(0)
  /** 包含全部且最新的表单 */
  const mergedParams = computed(() => {
    if (!buildFormData.value) return null

    let params = formatFormData(buildFormData.value)
    if (updatedFormItems.value.length > 0) {
      // 使用 Object.assign 合并 formItems，保持原有配置并覆盖更新的属性
      const originalFormItemsMap: Record<string, FormItem> = {}
      params.formItems.forEach(item => {
        if (item.property) {
          originalFormItemsMap[item.property] = item
        }
      })

      // 将更新的表单项合并到原有配置中
      updatedFormItems.value.forEach(updatedItem => {
        if (updatedItem.property) {
          originalFormItemsMap[updatedItem.property] = {
            ...originalFormItemsMap[updatedItem.property],
            ...updatedItem
          }
        }
      })

      params = {
        ...params,
        formItems: Object.values(originalFormItemsMap)
      }
    }

    return params
  })

  const allFormItemsMap = computed(() => {
    if (!mergedParams.value) return null

    const map: Record<string, FormItem> = {}
    mergedParams.value.formItems.forEach(item => {
      if (item.property) {
        map[item.property] = item
      }
    })
    return map
  })

  // 根据 currentSchema 和实际表单数据动态生成步骤信息
  const infoSteps = computed(() => {
    try {
      if (!buildFormData.value || !currentSchema.value) return []

      const params = mergedParams.value
      if (!params) return []

      // 获取所有非隐藏字段的属性名
      const allFormFields: string[] = params.formItems
        .filter(item => item.type !== 'hidden') // 排除隐藏字段
        .map(item => item.property)
        .filter((prop): prop is string => typeof prop === 'string' && prop.length > 0)

      // 获取所有表单数据来计算已填写的字段数量
      // 使用 submitFormData 来获取所有步骤的数据，因为它包含了所有字段的当前值
      const getAllFormData = () => {
        const allData: Record<string, any> = {}

        // 从 submitFormData 中获取所有字段的当前值
        if (submitFormData.value) {
          submitFormData.value.forEach(item => {
            if (item.property !== 'is_draft') {
              // 排除 is_draft 字段
              allData[item.property] = item.value
            }
          })
        }

        // 如果当前有表单实例，也从中获取最新的数据
        if (formRef.value?.formModel) {
          Object.assign(allData, formRef.value.formModel)
        }

        return allData
      }

      const allFormData = getAllFormData()
      // 只包含有实际表单项的组
      const validGroups = currentSchema.value
        ?.map((group, index) => {
          // 计算当前组中实际存在的可见表单项数量（已排除hidden类型）
          const actualFields = group.fields.filter(field => allFormFields.includes(field.field))

          // 计算当前组中已填写的表单项数量
          const completeCount = actualFields.filter(field => {
            const value = allFormData[field.field]
            // 检查值是否有效（不为空、null、undefined或空字符串）
            return (
              value !== null &&
              value !== undefined &&
              value !== '' &&
              (Array.isArray(value) ? value.length > 0 : true)
            )
          }).length

          return {
            title: group.title,
            code: group.code,
            fields: group.fields,
            actualFields: actualFields, // 实际存在的可见字段
            total: actualFields.length, // 实际可见表单项数量（不包含hidden）
            complete: completeCount, // 已填写的表单项数量
            active: false,
            done: completeCount === actualFields.length && actualFields.length > 0, // 当前组是否已完成
            originalIndex: index
          }
        })
        .filter(group => group.total > 0) // 只保留有可见表单项的组

      return validGroups || []
    } catch (error) {
      console.error('计算 infoSteps 时出错:', error)
      return []
    }
  })

  // 带激活状态的步骤信息 - 使用缓存优化性能
  const infoStepsWithActive = computed(() => {
    try {
      const steps = infoSteps.value
      const currentActiveStep = activeStep.value

      if (!steps || !Array.isArray(steps)) return []

      // 如果步骤数据没有变化，只更新激活状态
      return steps.map((step, index) => ({
        ...step,
        active: index === currentActiveStep
      }))
    } catch (error) {
      console.error('计算 infoStepsWithActive 时出错:', error)
      return []
    }
  })
  const onClickItem = async (i: number) => {
    if (i === activeStep.value) return

    await updateSubmitFormData()
    activeStep.value = i
  }

  // 当前激活的组信息
  const currentGroup = computed(() => {
    try {
      const steps = infoSteps.value
      const currentStep = activeStep.value

      if (!steps || !Array.isArray(steps) || currentStep < 0 || currentStep >= steps.length) {
        return null
      }

      return steps[currentStep] || null
    } catch (error) {
      console.error('计算 currentGroup 时出错:', error)
      return null
    }
  })

  // 更新提交表单数据
  const updateSubmitFormData = async (isDraft: boolean = true) => {
    if (!submitFormData.value) return

    return new Promise(resolve => {
      try {
        const formData = formRef.value?.formModel || {}
        console.log(formData, 'formData')

        // 更新 submitFormData 中的值
        submitFormData.value = submitFormData.value.map(item => ({
          ...item,
          value: formData[item.property] !== undefined ? formData[item.property] : item.value
        }))

        // 添加 is_draft 参数
        const isDraftParam = submitFormData.value.find(item => item.property === 'is_draft')
        if (isDraftParam) {
          isDraftParam.value = isDraft ? 1 : 0
        } else {
          submitFormData.value.push({
            property: 'is_draft',
            value: isDraft ? 1 : 0
          })
        }
        resolve(true)
      } catch (error) {
        console.warn('更新提交表单数据失败:', error)
        resolve(false)
      }
    })
  }

  const canSumit = computed(() => {
    return infoStepsWithActive.value.every(step => step.done)
  })

  const submitLoading = ref(false)
  // 提交按钮点击事件
  const handleSubmit = async () => {
    try {
      // 验证当前表单
      if (!formRef.value) {
        console.error('表单引用不存在')
        return
      }

      const isValid = await formRef.value.validate()
      if (!isValid) {
        console.log('表单验证失败，请检查必填项')
        return
      }

      // 更新提交表单数据（正式提交模式）
      await updateSubmitFormData(false)

      if (submitFormData.value && Array.isArray(submitFormData.value)) {
        const params = submitFormData.value.reduce(
          (acc, item) => {
            if (item && item.property) {
              acc[item.property] = item.value
            }
            return acc
          },
          {} as Record<string, any>
        )

        console.log('正式提交表单数据:', params)

        // 检查 action 是否存在
        if (!action) {
          throw new Error('SDK action 未正确初始化')
        }
        submitLoading.value = true
        await action.addInputs_parameter(params).execute()
        message.showToast('保存成功')
        submitLoading.value = false
        router.back()
      } else {
        console.error('提交表单数据未正确初始化')
        return
      }
    } catch (error) {
      console.error('提交表单时出错:', error)
      // 用户友好的错误提示
      if (error instanceof Error) {
        alert(`提交失败: ${error.message}`)
      } else {
        alert('提交失败，请稍后重试')
      }
    } finally {
      submitLoading.value = false
    }
  }

  const nextLoading = ref(false)
  // 保存并填写下一组按钮点击事件
  const handleSaveAndNext = async () => {
    try {
      // 验证当前表单
      // if (!formRef.value) {
      //   console.error('表单引用不存在')
      //   return
      // }

      const isValid = await formRef.value?.validate()
      if (!isValid) {
        return
      }

      // 更新提交表单数据（草稿模式）
      await updateSubmitFormData(true)

      if (submitFormData.value && Array.isArray(submitFormData.value)) {
        const params = submitFormData.value.reduce(
          (acc, item) => {
            if (item && item.property) {
              acc[item.property] = item.value
            }
            return acc
          },
          {} as Record<string, any>
        )

        console.log('保存草稿数据:', params)

        // 检查 action 是否存在
        if (!action) {
          throw new Error('SDK action 未正确初始化')
        }

        nextLoading.value = true
        const result = await action.addInputs_parameter(params).execute()
        console.log('草稿保存成功:', result)
      } else {
        console.error('提交表单数据未正确初始化')
        return
      }

      // 切换到下一组 - 直接切换，避免重复调用 updateSubmitFormData
      const nextIndex = activeStep.value + 1
      if (nextIndex < infoStepsWithActive.value.length) {
        activeStep.value = nextIndex
      }
    } catch (error) {
      console.error('保存并切换到下一组时出错:', error)
      // 用户友好的错误提示
      if (error instanceof Error) {
        alert(`保存失败: ${error.message}`)
      } else {
        alert('保存失败，请稍后重试')
      }
    } finally {
      nextLoading.value = false
    }
  }

  const currentStepparameters = computed(() => {
    try {
      // 需要表单数据和分组数据
      if (!buildFormData.value || !currentGroup.value || !allFormItemsMap.value) return null

      const params = mergedParams.value
      if (!params) return null

      // 获取当前组实际存在的字段列表
      if (!currentGroup.value.actualFields || !Array.isArray(currentGroup.value.actualFields)) {
        console.warn('currentGroup.actualFields 不是有效的数组')
        return null
      }
      // 过滤表单项，只显示当前组的字段
      const filteredFormItems: FormItem[] = []
      currentGroup.value.actualFields.forEach(item => {
        if (allFormItemsMap.value?.[item.field]) {
          const formItem = { ...allFormItemsMap.value[item.field] }
          // 用 submitFormData 里的值覆盖 default_value
          const found = submitFormData.value.find(f => f.property === item.field)
          if (found) {
            formItem.default_value = found.value
          }
          filteredFormItems.push(formItem)
        }
      })

      return {
        ...params,
        formItems: filteredFormItems
      }
    } catch (error) {
      console.error('计算 parameters 时出错:', error)
      return null
    }
  })

  defineOptions({
    name: 'CollectionTaskDetail'
  })
</script>

<style>
  .active {
    background: linear-gradient(180deg, #3c57e1 0%, #748bff 100%);
  }
</style>
